#include "NotificationManager.h"
#include <QApplication>
#include <QAction>
#include <QRandomGenerator>
#include <QDebug>

NotificationManager::NotificationManager(QObject *parent)
    : QObject(parent)
    , m_trayIcon(nullptr)
    , m_tray<PERSON>enu(nullptr)
    , m_dailyReminderTimer(new QTimer(this))
    , m_motivationalTimer(new QTimer(this))
    , m_motivationalEnabled(true)
{
    m_motivationalMessages = QStringList{
        "你今天过得怎么样？记得记录你的心情！",
        "每一天的坚持都让你更强大！",
        "相信自己，你正在创造奇迹！",
        "今天也要加油哦！",
        "记住你的目标，为什么要开始这个旅程？",
        "困难只是暂时的，但你的成长是永久的！",
        "你已经走了这么远，不要放弃！",
        "每个选择都在塑造更好的你！"
    };
}

NotificationManager::~NotificationManager()
{
    if (m_trayIcon) {
        m_trayIcon->hide();
    }
}

void NotificationManager::initialize()
{
    if (!QSystemTrayIcon::isSystemTrayAvailable()) {
        qWarning() << "系统托盘不可用";
        return;
    }
    
    createTrayIcon();
    setupTimers();
    
    m_trayIcon->show();
}

void NotificationManager::createTrayIcon()
{
    m_trayIcon = new QSystemTrayIcon(this);
    m_trayIcon->setIcon(QIcon(":/resources/icons/app_icon.png"));
    m_trayIcon->setToolTip("戒瘾助手 - QuitAddiction");
    
    // 创建托盘菜单
    m_trayMenu = new QMenu();
    
    QAction* showAction = m_trayMenu->addAction("显示主窗口");
    connect(showAction, &QAction::triggered, [this]() {
        emit showMainWindow();
    });
    
    QAction* emergencyAction = m_trayMenu->addAction("紧急求助");
    connect(emergencyAction, &QAction::triggered, this, &NotificationManager::showEmergencyAlert);
    
    m_trayMenu->addSeparator();
    
    QAction* quitAction = m_trayMenu->addAction("退出");
    connect(quitAction, &QAction::triggered, qApp, &QApplication::quit);
    
    m_trayIcon->setContextMenu(m_trayMenu);
    
    connect(m_trayIcon, &QSystemTrayIcon::activated, 
            this, &NotificationManager::onTrayIconActivated);
}

void NotificationManager::setupTimers()
{
    // 每日提醒定时器
    connect(m_dailyReminderTimer, &QTimer::timeout, 
            this, &NotificationManager::onDailyReminderTriggered);
    
    // 励志消息定时器 (每2小时)
    m_motivationalTimer->setInterval(2 * 60 * 60 * 1000);
    connect(m_motivationalTimer, &QTimer::timeout, 
            this, &NotificationManager::onMotivationalReminderTriggered);
    
    if (m_motivationalEnabled) {
        m_motivationalTimer->start();
    }
}

void NotificationManager::showNotification(const QString& title, const QString& message, 
                                         QSystemTrayIcon::MessageIcon icon)
{
    if (m_trayIcon && m_trayIcon->isVisible()) {
        m_trayIcon->showMessage(title, message, icon, 5000);
    }
}

void NotificationManager::setDailyReminder(const QTime& time, const QString& message)
{
    Q_UNUSED(message)
    
    // 计算到下次提醒的毫秒数
    QDateTime now = QDateTime::currentDateTime();
    QDateTime nextReminder = QDateTime(now.date(), time);
    
    if (nextReminder <= now) {
        nextReminder = nextReminder.addDays(1);
    }
    
    qint64 msToNext = now.msecsTo(nextReminder);
    m_dailyReminderTimer->start(static_cast<int>(msToNext));
}

void NotificationManager::setMotivationalReminders(bool enabled)
{
    m_motivationalEnabled = enabled;
    
    if (enabled) {
        m_motivationalTimer->start();
    } else {
        m_motivationalTimer->stop();
    }
}

void NotificationManager::setDangerTimeAlerts(const QList<QTime>& times)
{
    // 清除现有定时器
    for (QTimer* timer : m_dangerTimeTimers) {
        timer->deleteLater();
    }
    m_dangerTimeTimers.clear();
    
    // 为每个危险时段创建定时器
    for (const QTime& time : times) {
        QTimer* timer = new QTimer(this);
        connect(timer, &QTimer::timeout, [this]() {
            showNotification("危险时段提醒", 
                           "现在是您设置的危险时段，请保持警惕！\n建议进行冥想或其他健康活动。",
                           QSystemTrayIcon::Warning);
        });
        
        // 计算到下次提醒的时间
        QDateTime now = QDateTime::currentDateTime();
        QDateTime nextAlert = QDateTime(now.date(), time);
        
        if (nextAlert <= now) {
            nextAlert = nextAlert.addDays(1);
        }
        
        qint64 msToNext = now.msecsTo(nextAlert);
        timer->start(static_cast<int>(msToNext));
        
        m_dangerTimeTimers.append(timer);
    }
}

void NotificationManager::showEmergencyAlert()
{
    showNotification("紧急求助",
                   "请记住：这只是暂时的感觉。\n深呼吸，联系支持者，或进行替代活动。",
                   QSystemTrayIcon::Critical);

    emit emergencyHelpRequested();
}

void NotificationManager::onTrayIconActivated(QSystemTrayIcon::ActivationReason reason)
{
    switch (reason) {
    case QSystemTrayIcon::DoubleClick:
        emit showMainWindow();
        break;
    case QSystemTrayIcon::Trigger:
        // 单击显示菜单
        break;
    default:
        break;
    }
}

void NotificationManager::onDailyReminderTriggered()
{
    showNotification("每日提醒",
                   "记得记录今天的心情和感受！\n坚持就是胜利！",
                   QSystemTrayIcon::Information);

    // 重新设置明天的提醒
    UserSettings settings = loadUserSettings(); // 需要访问DataManager
    setDailyReminder(settings.dailyReminderTime);
}

void NotificationManager::onMotivationalReminderTriggered()
{
    QString message = getRandomMotivationalMessage();
    showNotification("励志提醒", message, QSystemTrayIcon::Information);
}

QString NotificationManager::getRandomMotivationalMessage() const
{
    if (m_motivationalMessages.isEmpty()) {
        return "继续加油！你正在做得很好！";
    }

    int index = QRandomGenerator::global()->bounded(m_motivationalMessages.size());
    return m_motivationalMessages.at(index);
}