#ifndef SETTINGSWIDGET_H
#define SETTINGSWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QLabel>
#include <QLineEdit>
#include <QTextEdit>
#include <QDateEdit>
#include <QTimeEdit>
#include <QCheckBox>
#include <QSpinBox>
#include <QComboBox>
#include <QPushButton>
#include <QListWidget>
#include <QSlider>

class DataManager;

class SettingsWidget : public QWidget
{
    Q_OBJECT

public:
    explicit SettingsWidget(DataManager* dataManager, QWidget *parent = nullptr);

public slots:
    void loadSettings();
    void saveSettings();

private slots:
    void onAddDangerTime();
    void onRemoveDangerTime();
    void onResetToDefaults();
    void onExportSettings();
    void onImportSettings();
    void onThemeChanged();

private:
    void setupUI();
    void setupGeneralSettings();
    void setupNotificationSettings();
    void setupDangerTimeSettings();
    void setupAppearanceSettings();
    void setupDataSettings();
    void connectSignals();

private:
    DataManager* m_dataManager;
    
    // 通用设置
    QLineEdit* m_userNameEdit;
    QTextEdit* m_goalEdit;
    QDateEdit* m_startDateEdit;
    
    // 通知设置
    QCheckBox* m_dailyReminderCheck;
    QTimeEdit* m_dailyReminderTime;
    QCheckBox* m_motivationalCheck;
    QSpinBox* m_motivationalInterval;
    
    // 危险时段设置
    QListWidget* m_dangerTimesList;
    QTimeEdit* m_newDangerTime;
    QPushButton* m_addDangerTimeBtn;
    QPushButton* m_removeDangerTimeBtn;
    
    // 外观设置
    QComboBox* m_themeCombo;
    QComboBox* m_languageCombo;
    QSlider* m_opacitySlider;
    QCheckBox* m_minimizeToTrayCheck;
    QCheckBox* m_startMinimizedCheck;
    
    // 数据设置
    QPushButton* m_exportDataBtn;
    QPushButton* m_importDataBtn;
    QPushButton* m_backupDataBtn;
    QPushButton* m_resetDataBtn;
    
    // 按钮
    QPushButton* m_saveBtn;
    QPushButton* m_cancelBtn;
    QPushButton* m_resetBtn;
};

// 紧急求助设置页面
class EmergencySettingsWidget : public QWidget
{
    Q_OBJECT

public:
    explicit EmergencySettingsWidget(QWidget *parent = nullptr);

private slots:
    void onAddContact();
    void onRemoveContact();
    void onTestEmergencyAlert();

private:
    void setupUI();
    void loadEmergencyContacts();
    void saveEmergencyContacts();

private:
    QListWidget* m_contactsList;
    QLineEdit* m_contactNameEdit;
    QLineEdit* m_contactPhoneEdit;
    QLineEdit* m_contactEmailEdit;
    QPushButton* m_addContactBtn;
    QPushButton* m_removeContactBtn;
    QPushButton* m_testAlertBtn;
    
    QTextEdit* m_emergencyMessageEdit;
    QCheckBox* m_autoCallCheck;
    QCheckBox* m_autoEmailCheck;
    QCheckBox* m_showEmergencyDialogCheck;
};

// 冥想设置页面
class MeditationSettingsWidget : public QWidget
{
    Q_OBJECT

public:
    explicit MeditationSettingsWidget(QWidget *parent = nullptr);

private slots:
    void onDefaultDurationChanged();
    void onSoundChanged();
    void onAutoStartChanged();

private:
    void setupUI();
    void loadMeditationSettings();
    void saveMeditationSettings();

private:
    QComboBox* m_defaultDurationCombo;
    QComboBox* m_meditationSoundCombo;
    QSlider* m_volumeSlider;
    QCheckBox* m_autoStartCheck;
    QCheckBox* m_reminderCheck;
    QTimeEdit* m_reminderTime;
    QSpinBox* m_reminderInterval;
    
    QPushButton* m_previewSoundBtn;
    QPushButton* m_customSoundBtn;
};

#endif // SETTINGSWIDGET_H
