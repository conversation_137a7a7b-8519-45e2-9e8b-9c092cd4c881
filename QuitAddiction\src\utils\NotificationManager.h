#ifndef NOTIFICATIONMANAGER_H
#define NOTIFICATIONMANAGER_H

#include <QObject>
#include <QSystemTrayIcon>
#include <QMenu>
#include <QTimer>
#include <QTime>
#include <QStringList>
#include <QList>

class NotificationManager : public QObject
{
    Q_OBJECT

public:
    explicit NotificationManager(QObject *parent = nullptr);
    ~NotificationManager();

    // 初始化系统托盘
    void initialize();
    
    // 显示通知
    void showNotification(const QString& title, const QString& message, 
                         QSystemTrayIcon::MessageIcon icon = QSystemTrayIcon::Information);
    
    // 设置每日提醒
    void setDailyReminder(const QTime& time, const QString& message = QString());
    
    // 设置励志消息提醒
    void setMotivationalReminders(bool enabled);
    
    // 设置危险时段警报
    void setDangerTimeAlerts(const QList<QTime>& times);
    
    // 显示紧急求助对话框
    void showEmergencyAlert();

signals:
    void showMainWindow();
    void emergencyHelpRequested();

private slots:
    void onTrayIconActivated(QSystemTrayIcon::ActivationReason reason);
    void onDailyReminderTriggered();
    void onMotivationalReminderTriggered();

private:
    void createTrayIcon();
    void setupTimers();
    QString getRandomMotivationalMessage() const;

private:
    QSystemTrayIcon* m_trayIcon;
    QMenu* m_trayMenu;
    
    // 定时器
    QTimer* m_dailyReminderTimer;
    QTimer* m_motivationalTimer;
    QList<QTimer*> m_dangerTimeTimers;
    
    // 配置
    bool m_motivationalEnabled;
    QStringList m_motivationalMessages;
};

#endif // NOTIFICATIONMANAGER_H
