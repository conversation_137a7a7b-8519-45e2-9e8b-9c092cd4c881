#include <QApplication>
#include <QDir>
#include <QStandardPaths>
#include <QMessageBox>
#include <QTranslator>
#include <QLocale>
#include <QStyleFactory>
#include <QFontDatabase>

#include "ui/MainWindow.h"
#include "core/DataManager.h"
#include "utils/NotificationManager.h"

// 设置应用程序数据目录
void setupApplicationPaths()
{
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir appDataDir(appDataPath);
    
    if (!appDataDir.exists()) {
        appDataDir.mkpath(".");
    }
    
    // 创建子目录
    appDataDir.mkpath("data");
    appDataDir.mkpath("logs");
    appDataDir.mkpath("exports");
    appDataDir.mkpath("backups");
}

// 设置应用程序样式
void setupApplicationStyle(QApplication& app)
{
    // 设置应用程序样式
    app.setStyle(QStyleFactory::create("Fusion"));
    
    // 设置深色主题（可选）
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    
    // 可以通过设置来启用深色主题
    // app.setPalette(darkPalette);
}

// 加载字体
void loadCustomFonts()
{
    // 加载自定义字体（如果有的话）
    QFontDatabase::addApplicationFont(":/fonts/SourceHanSansCN-Regular.otf");
}

// 设置国际化
void setupInternationalization(QApplication& app)
{
    QTranslator translator;
    const QStringList uiLanguages = QLocale::system().uiLanguages();
    
    for (const QString &locale : uiLanguages) {
        const QString baseName = "QuitAddiction_" + QLocale(locale).name();
        if (translator.load(":/i18n/" + baseName)) {
            app.installTranslator(&translator);
            break;
        }
    }
}

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("QuitAddiction");
    app.setApplicationDisplayName("戒瘾助手");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("QuitAddiction Team");
    app.setOrganizationDomain("quitaddiction.org");
    
    // 设置应用程序图标
    app.setWindowIcon(QIcon(":/resources/icons/app_icon.png"));
    
    // 检查是否已有实例在运行（单实例应用）
    // 这里可以添加单实例检查逻辑
    
    // 初始化应用程序
    setupApplicationPaths();
    setupApplicationStyle(app);
    loadCustomFonts();
    setupInternationalization(app);
    
    // 创建主窗口
    MainWindow window;
    
    // 检查命令行参数
    QStringList arguments = app.arguments();
    bool startMinimized = arguments.contains("--minimized") || arguments.contains("-m");
    
    if (!startMinimized) {
        window.show();
    }
    
    return app.exec();
}
