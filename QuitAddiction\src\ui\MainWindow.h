#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QStackedWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QTextEdit>
#include <QSlider>
#include <QSpinBox>
#include <QDateEdit>
#include <QTimeEdit>
#include <QListWidget>
#include <QProgressBar>
#include <QTabWidget>

class DataManager;
class NotificationManager;
class StatisticsWidget;
class DashboardWidget;
class DiaryWidget;
class SettingsWidget;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;
    void changeEvent(QEvent *event) override;

private slots:
    void showFromTray();
    void onEmergencyHelp();
    void onTabChanged(int index);

private:
    void setupUI();
    void setupMenuBar();
    void setupStatusBar();
    void connectSignals();
    void loadSettings();
    void saveSettings();

private:
    // 核心组件
    DataManager* m_dataManager;
    NotificationManager* m_notificationManager;
    
    // UI组件
    QTabWidget* m_tabWidget;
    DashboardWidget* m_dashboardWidget;
    DiaryWidget* m_diaryWidget;
    StatisticsWidget* m_statisticsWidget;
    SettingsWidget* m_settingsWidget;
    
    // 状态栏
    QLabel* m_statusLabel;
    QLabel* m_streakLabel;
    
    // 设置
    bool m_minimizeToTray;
    bool m_startMinimized;
};

// 仪表盘页面
class DashboardWidget : public QWidget
{
    Q_OBJECT

public:
    explicit DashboardWidget(DataManager* dataManager, QWidget *parent = nullptr);

public slots:
    void updateDisplay();

private slots:
    void onEmergencyButtonClicked();
    void onQuickNoteClicked();

private:
    void setupUI();
    void updateStreakDisplay();
    void updateMotivationalMessage();

private:
    DataManager* m_dataManager;
    
    QLabel* m_streakLabel;
    QLabel* m_totalDaysLabel;
    QLabel* m_successRateLabel;
    QLabel* m_motivationalLabel;
    QPushButton* m_emergencyButton;
    QPushButton* m_quickNoteButton;
    QProgressBar* m_dailyProgressBar;
};

// 日记页面
class DiaryWidget : public QWidget
{
    Q_OBJECT

public:
    explicit DiaryWidget(DataManager* dataManager, QWidget *parent = nullptr);

public slots:
    void loadTodayRecord();

private slots:
    void onSaveClicked();
    void onDateChanged();
    void onMoodChanged(int value);

private:
    void setupUI();
    void loadRecord(const QDate& date);
    void saveCurrentRecord();

private:
    DataManager* m_dataManager;
    
    QDateEdit* m_dateEdit;
    QSlider* m_moodSlider;
    QLabel* m_moodLabel;
    QTextEdit* m_notesEdit;
    QSlider* m_urgeSlider;
    QLabel* m_urgeLabel;
    QListWidget* m_triggersList;
    QListWidget* m_activitiesList;
    QPushButton* m_saveButton;
};

#endif // MAINWINDOW_H
