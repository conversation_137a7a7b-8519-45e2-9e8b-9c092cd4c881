cmake_minimum_required(VERSION 3.16)

project(QuitAddiction VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt6组件
find_package(Qt6 REQUIRED COMPONENTS 
    Core 
    Widgets 
    Sql 
    Charts
    Multimedia
    Network
)

# 启用Qt的MOC、UIC、RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# 设置源文件目录
set(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src)

# 收集源文件
file(GLOB_RECURSE SOURCES
    "${SRC_DIR}/*.cpp"
    "${SRC_DIR}/*.h"
)

# 收集UI文件
file(GLOB_RECURSE UI_FILES
    "${SRC_DIR}/*.ui"
)

# 收集资源文件
set(RESOURCE_FILES
    resources/resources.qrc
)

# 创建可执行文件
add_executable(QuitAddiction
    ${SOURCES}
    ${UI_FILES}
    ${RESOURCE_FILES}
)

# 链接Qt库
target_link_libraries(QuitAddiction
    Qt6::Core
    Qt6::Widgets
    Qt6::Sql
    Qt6::Charts
    Qt6::Multimedia
    Qt6::Network
)

# 设置包含目录
target_include_directories(QuitAddiction PRIVATE
    ${SRC_DIR}
    ${SRC_DIR}/core
    ${SRC_DIR}/ui
    ${SRC_DIR}/widgets
    ${SRC_DIR}/utils
)

# Windows特定设置
if(WIN32)
    set_target_properties(QuitAddiction PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
    
    # 设置应用程序图标
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/resources/app.ico")
        set(APP_ICON "${CMAKE_CURRENT_SOURCE_DIR}/resources/app.ico")
        target_sources(QuitAddiction PRIVATE ${APP_ICON})
    endif()
endif()

# macOS特定设置
if(APPLE)
    set_target_properties(QuitAddiction PROPERTIES
        MACOSX_BUNDLE TRUE
        MACOSX_BUNDLE_INFO_PLIST "${CMAKE_CURRENT_SOURCE_DIR}/Info.plist"
    )
endif()

# 安装规则
install(TARGETS QuitAddiction
    BUNDLE DESTINATION .
    RUNTIME DESTINATION bin
)

# Qt部署
if(WIN32)
    find_program(QT_WINDEPLOYQT_EXECUTABLE windeployqt HINTS ${Qt6_DIR}/../../../bin)
    if(QT_WINDEPLOYQT_EXECUTABLE)
        add_custom_command(TARGET QuitAddiction POST_BUILD
            COMMAND ${QT_WINDEPLOYQT_EXECUTABLE} $<TARGET_FILE:QuitAddiction>
            COMMENT "Deploying Qt libraries"
        )
    endif()
endif()

# 编译选项
if(MSVC)
    target_compile_options(QuitAddiction PRIVATE /W4)
else()
    target_compile_options(QuitAddiction PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Debug配置
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(QuitAddiction PRIVATE DEBUG_MODE)
endif()
