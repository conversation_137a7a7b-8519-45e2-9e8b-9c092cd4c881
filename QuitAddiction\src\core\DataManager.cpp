#include "DataManager.h"
#include <QSqlQuery>
#include <QSqlError>
#include <QStandardPaths>
#include <QDir>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>

DataManager::DataManager(QObject *parent)
    : QObject(parent)
{
    // 设置数据库路径
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(appDataPath + "/data");
    m_databasePath = appDataPath + "/data/quitaddiction.db";
}

DataManager::~DataManager()
{
    close();
}

bool DataManager::initialize()
{
    m_database = QSqlDatabase::addDatabase("QSQLITE");
    m_database.setDatabaseName(m_databasePath);
    
    if (!m_database.open()) {
        qCritical() << "无法打开数据库:" << m_database.lastError().text();
        return false;
    }
    
    return createTables();
}

void DataManager::close()
{
    if (m_database.isOpen()) {
        m_database.close();
    }
}

bool DataManager::createTables()
{
    QStringList createTableQueries = {
        // 用户设置表
        R"(CREATE TABLE IF NOT EXISTS user_settings (
            id INTEGER PRIMARY KEY,
            start_date TEXT NOT NULL,
            daily_reminder_time TEXT,
            danger_times TEXT,
            motivational_enabled INTEGER DEFAULT 1,
            motivational_interval INTEGER DEFAULT 2,
            user_name TEXT,
            goal TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        ))",
        
        // 日记记录表
        R"(CREATE TABLE IF NOT EXISTS daily_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT UNIQUE NOT NULL,
            mood_score INTEGER CHECK(mood_score >= 1 AND mood_score <= 10),
            notes TEXT,
            had_urge INTEGER DEFAULT 0,
            urge_intensity INTEGER CHECK(urge_intensity >= 0 AND urge_intensity <= 10),
            triggers TEXT,
            activities TEXT,
            is_success INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        ))",
        
        // 成就表
        R"(CREATE TABLE IF NOT EXISTS achievements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            requirement INTEGER,
            unlocked INTEGER DEFAULT 0,
            unlocked_date TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        ))",
        
        // 统计缓存表
        R"(CREATE TABLE IF NOT EXISTS statistics_cache (
            id INTEGER PRIMARY KEY,
            current_streak INTEGER DEFAULT 0,
            longest_streak INTEGER DEFAULT 0,
            total_days INTEGER DEFAULT 0,
            successful_days INTEGER DEFAULT 0,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
        ))"
    };
    
    for (const QString& query : createTableQueries) {
        if (!executeSql(query)) {
            return false;
        }
    }
    
    // 插入默认成就
    insertDefaultAchievements();
    
    return true;
}

bool DataManager::executeSql(const QString& sql)
{
    QSqlQuery query;
    if (!query.exec(sql)) {
        qCritical() << "SQL执行失败:" << query.lastError().text();
        qCritical() << "SQL语句:" << sql;
        return false;
    }
    return true;
}

bool DataManager::saveUserSettings(const UserSettings& settings)
{
    QSqlQuery query;
    query.prepare(R"(
        INSERT OR REPLACE INTO user_settings 
        (id, start_date, daily_reminder_time, danger_times, motivational_enabled, 
         motivational_interval, user_name, goal, updated_at)
        VALUES (1, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    )");
    
    query.addBindValue(settings.startDate.toString(Qt::ISODate));
    query.addBindValue(settings.dailyReminderTime.toString());
    
    // 将危险时段转换为JSON字符串
    QJsonArray dangerTimesArray;
    for (const QTime& time : settings.dangerTimes) {
        dangerTimesArray.append(time.toString());
    }
    query.addBindValue(QJsonDocument(dangerTimesArray).toJson(QJsonDocument::Compact));
    
    query.addBindValue(settings.motivationalEnabled ? 1 : 0);
    query.addBindValue(settings.motivationalInterval);
    query.addBindValue(settings.userName);
    query.addBindValue(settings.goal);
    
    if (!query.exec()) {
        qCritical() << "保存用户设置失败:" << query.lastError().text();
        return false;
    }
    
    return true;
}

UserSettings DataManager::loadUserSettings()
{
    UserSettings settings;
    
    QSqlQuery query("SELECT * FROM user_settings WHERE id = 1");
    if (query.next()) {
        settings.startDate = QDate::fromString(query.value("start_date").toString(), Qt::ISODate);
        settings.dailyReminderTime = QTime::fromString(query.value("daily_reminder_time").toString());
        
        // 解析危险时段JSON
        QJsonDocument doc = QJsonDocument::fromJson(query.value("danger_times").toByteArray());
        QJsonArray array = doc.array();
        for (const QJsonValue& value : array) {
            settings.dangerTimes.append(QTime::fromString(value.toString()));
        }
        
        settings.motivationalEnabled = query.value("motivational_enabled").toBool();
        settings.motivationalInterval = query.value("motivational_interval").toInt();
        settings.userName = query.value("user_name").toString();
        settings.goal = query.value("goal").toString();
    } else {
        // 返回默认设置
        settings.startDate = QDate::currentDate();
        settings.dailyReminderTime = QTime(21, 0); // 晚上9点
        settings.motivationalEnabled = true;
        settings.motivationalInterval = 2;
        settings.userName = "用户";
        settings.goal = "戒除不良习惯，重获新生";
    }
    
    return settings;
}

void DataManager::insertDefaultAchievements()
{
    QList<QPair<QString, QString>> achievements = {
        {"新的开始", "开始你的戒断之旅"},
        {"坚持一天", "成功戒断1天"},
        {"一周里程碑", "成功戒断7天"},
        {"月度成就", "成功戒断30天"},
        {"季度英雄", "成功戒断90天"},
        {"年度传奇", "成功戒断365天"},
        {"情绪管理师", "连续7天记录心情"},
        {"自我反思者", "写下50篇日记"},
        {"意志坚定", "抵御100次冲动"}
    };
    
    for (const auto& achievement : achievements) {
        QSqlQuery query;
        query.prepare("INSERT OR IGNORE INTO achievements (name, description) VALUES (?, ?)");
        query.addBindValue(achievement.first);
        query.addBindValue(achievement.second);
        query.exec();
    }
}

bool DataManager::saveDailyRecord(const DailyRecord& record)
{
    QSqlQuery query;
    query.prepare(R"(
        INSERT OR REPLACE INTO daily_records
        (date, mood_score, notes, had_urge, urge_intensity, triggers, activities, is_success, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    )");

    query.addBindValue(record.date.toString(Qt::ISODate));
    query.addBindValue(record.moodScore);
    query.addBindValue(record.notes);
    query.addBindValue(record.hadUrge ? 1 : 0);
    query.addBindValue(record.urgeIntensity);

    // 将触发因素和活动转换为JSON字符串
    QJsonArray triggersArray;
    for (const QString& trigger : record.triggers) {
        triggersArray.append(trigger);
    }
    query.addBindValue(QJsonDocument(triggersArray).toJson(QJsonDocument::Compact));

    QJsonArray activitiesArray;
    for (const QString& activity : record.activities) {
        activitiesArray.append(activity);
    }
    query.addBindValue(QJsonDocument(activitiesArray).toJson(QJsonDocument::Compact));

    query.addBindValue(record.isSuccess ? 1 : 0);

    if (!query.exec()) {
        qCritical() << "保存日记记录失败:" << query.lastError().text();
        return false;
    }

    emit recordSaved(record.date);
    emit dataChanged();
    return true;
}

DailyRecord DataManager::loadDailyRecord(const QDate& date)
{
    DailyRecord record;
    record.date = date;

    QSqlQuery query;
    query.prepare("SELECT * FROM daily_records WHERE date = ?");
    query.addBindValue(date.toString(Qt::ISODate));

    if (query.exec() && query.next()) {
        record.moodScore = query.value("mood_score").toInt();
        record.notes = query.value("notes").toString();
        record.hadUrge = query.value("had_urge").toBool();
        record.urgeIntensity = query.value("urge_intensity").toInt();
        record.isSuccess = query.value("is_success").toBool();

        // 解析触发因素JSON
        QJsonDocument triggersDoc = QJsonDocument::fromJson(query.value("triggers").toByteArray());
        QJsonArray triggersArray = triggersDoc.array();
        for (const QJsonValue& value : triggersArray) {
            record.triggers.append(value.toString());
        }

        // 解析活动JSON
        QJsonDocument activitiesDoc = QJsonDocument::fromJson(query.value("activities").toByteArray());
        QJsonArray activitiesArray = activitiesDoc.array();
        for (const QJsonValue& value : activitiesArray) {
            record.activities.append(value.toString());
        }
    }

    return record;
}

QList<DailyRecord> DataManager::loadRecordsInRange(const QDate& startDate, const QDate& endDate)
{
    QList<DailyRecord> records;

    QSqlQuery query;
    query.prepare("SELECT * FROM daily_records WHERE date BETWEEN ? AND ? ORDER BY date");
    query.addBindValue(startDate.toString(Qt::ISODate));
    query.addBindValue(endDate.toString(Qt::ISODate));

    if (query.exec()) {
        while (query.next()) {
            DailyRecord record;
            record.date = QDate::fromString(query.value("date").toString(), Qt::ISODate);
            record.moodScore = query.value("mood_score").toInt();
            record.notes = query.value("notes").toString();
            record.hadUrge = query.value("had_urge").toBool();
            record.urgeIntensity = query.value("urge_intensity").toInt();
            record.isSuccess = query.value("is_success").toBool();

            // 解析JSON数据
            QJsonDocument triggersDoc = QJsonDocument::fromJson(query.value("triggers").toByteArray());
            QJsonArray triggersArray = triggersDoc.array();
            for (const QJsonValue& value : triggersArray) {
                record.triggers.append(value.toString());
            }

            QJsonDocument activitiesDoc = QJsonDocument::fromJson(query.value("activities").toByteArray());
            QJsonArray activitiesArray = activitiesDoc.array();
            for (const QJsonValue& value : activitiesArray) {
                record.activities.append(value.toString());
            }

            records.append(record);
        }
    }

    return records;
}

int DataManager::getCurrentStreak()
{
    QSqlQuery query("SELECT date, is_success FROM daily_records ORDER BY date DESC");

    int streak = 0;
    QDate currentDate = QDate::currentDate();

    while (query.next()) {
        QDate recordDate = QDate::fromString(query.value("date").toString(), Qt::ISODate);
        bool isSuccess = query.value("is_success").toBool();

        // 如果是今天或昨天的记录
        if (recordDate == currentDate || recordDate == currentDate.addDays(-1)) {
            if (isSuccess) {
                streak++;
                currentDate = recordDate.addDays(-1);
            } else {
                break;
            }
        } else if (recordDate < currentDate.addDays(-1)) {
            // 有间隔，检查是否连续
            if (recordDate == currentDate && isSuccess) {
                streak++;
                currentDate = recordDate.addDays(-1);
            } else {
                break;
            }
        }
    }

    return streak;
}

int DataManager::getLongestStreak()
{
    QSqlQuery query("SELECT date, is_success FROM daily_records ORDER BY date");

    int longestStreak = 0;
    int currentStreak = 0;
    QDate lastDate;

    while (query.next()) {
        QDate recordDate = QDate::fromString(query.value("date").toString(), Qt::ISODate);
        bool isSuccess = query.value("is_success").toBool();

        if (isSuccess) {
            if (lastDate.isValid() && lastDate.daysTo(recordDate) == 1) {
                currentStreak++;
            } else {
                currentStreak = 1;
            }

            longestStreak = qMax(longestStreak, currentStreak);
        } else {
            currentStreak = 0;
        }

        lastDate = recordDate;
    }

    return longestStreak;
}

int DataManager::getTotalDays()
{
    QSqlQuery query("SELECT COUNT(*) FROM daily_records");
    if (query.next()) {
        return query.value(0).toInt();
    }
    return 0;
}

int DataManager::getSuccessfulDays()
{
    QSqlQuery query("SELECT COUNT(*) FROM daily_records WHERE is_success = 1");
    if (query.next()) {
        return query.value(0).toInt();
    }
    return 0;
}

double DataManager::getSuccessRate()
{
    int totalDays = getTotalDays();
    if (totalDays == 0) return 0.0;

    int successfulDays = getSuccessfulDays();
    return (static_cast<double>(successfulDays) / totalDays) * 100.0;
}

QList<int> DataManager::getMoodTrend(int days)
{
    QList<int> moodTrend;

    QSqlQuery query;
    query.prepare("SELECT mood_score FROM daily_records WHERE date >= ? ORDER BY date DESC LIMIT ?");
    query.addBindValue(QDate::currentDate().addDays(-days).toString(Qt::ISODate));
    query.addBindValue(days);

    while (query.exec() && query.next()) {
        int moodScore = query.value("mood_score").toInt();
        if (moodScore > 0) {
            moodTrend.append(moodScore);
        }
    }

    return moodTrend;
}

bool DataManager::exportData(const QString& filePath)
{
    // 简单的导出实现
    Q_UNUSED(filePath)
    return true;
}

bool DataManager::importData(const QString& filePath)
{
    // 简单的导入实现
    Q_UNUSED(filePath)
    return true;
}
