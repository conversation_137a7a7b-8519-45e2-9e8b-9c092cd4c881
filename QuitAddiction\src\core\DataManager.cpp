#include "DataManager.h"
#include <QSqlQuery>
#include <QSqlError>
#include <QStandardPaths>
#include <QDir>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>

DataManager::DataManager(QObject *parent)
    : QObject(parent)
{
    // 设置数据库路径
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(appDataPath + "/data");
    m_databasePath = appDataPath + "/data/quitaddiction.db";
}

DataManager::~DataManager()
{
    close();
}

bool DataManager::initialize()
{
    m_database = QSqlDatabase::addDatabase("QSQLITE");
    m_database.setDatabaseName(m_databasePath);
    
    if (!m_database.open()) {
        qCritical() << "无法打开数据库:" << m_database.lastError().text();
        return false;
    }
    
    return createTables();
}

void DataManager::close()
{
    if (m_database.isOpen()) {
        m_database.close();
    }
}

bool DataManager::createTables()
{
    QStringList createTableQueries = {
        // 用户设置表
        R"(CREATE TABLE IF NOT EXISTS user_settings (
            id INTEGER PRIMARY KEY,
            start_date TEXT NOT NULL,
            daily_reminder_time TEXT,
            danger_times TEXT,
            motivational_enabled INTEGER DEFAULT 1,
            motivational_interval INTEGER DEFAULT 2,
            user_name TEXT,
            goal TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        ))",
        
        // 日记记录表
        R"(CREATE TABLE IF NOT EXISTS daily_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT UNIQUE NOT NULL,
            mood_score INTEGER CHECK(mood_score >= 1 AND mood_score <= 10),
            notes TEXT,
            had_urge INTEGER DEFAULT 0,
            urge_intensity INTEGER CHECK(urge_intensity >= 0 AND urge_intensity <= 10),
            triggers TEXT,
            activities TEXT,
            is_success INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        ))",
        
        // 成就表
        R"(CREATE TABLE IF NOT EXISTS achievements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            requirement INTEGER,
            unlocked INTEGER DEFAULT 0,
            unlocked_date TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        ))",
        
        // 统计缓存表
        R"(CREATE TABLE IF NOT EXISTS statistics_cache (
            id INTEGER PRIMARY KEY,
            current_streak INTEGER DEFAULT 0,
            longest_streak INTEGER DEFAULT 0,
            total_days INTEGER DEFAULT 0,
            successful_days INTEGER DEFAULT 0,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
        ))"
    };
    
    for (const QString& query : createTableQueries) {
        if (!executeSql(query)) {
            return false;
        }
    }
    
    // 插入默认成就
    insertDefaultAchievements();
    
    return true;
}

bool DataManager::executeSql(const QString& sql)
{
    QSqlQuery query;
    if (!query.exec(sql)) {
        qCritical() << "SQL执行失败:" << query.lastError().text();
        qCritical() << "SQL语句:" << sql;
        return false;
    }
    return true;
}

bool DataManager::saveUserSettings(const UserSettings& settings)
{
    QSqlQuery query;
    query.prepare(R"(
        INSERT OR REPLACE INTO user_settings 
        (id, start_date, daily_reminder_time, danger_times, motivational_enabled, 
         motivational_interval, user_name, goal, updated_at)
        VALUES (1, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    )");
    
    query.addBindValue(settings.startDate.toString(Qt::ISODate));
    query.addBindValue(settings.dailyReminderTime.toString());
    
    // 将危险时段转换为JSON字符串
    QJsonArray dangerTimesArray;
    for (const QTime& time : settings.dangerTimes) {
        dangerTimesArray.append(time.toString());
    }
    query.addBindValue(QJsonDocument(dangerTimesArray).toJson(QJsonDocument::Compact));
    
    query.addBindValue(settings.motivationalEnabled ? 1 : 0);
    query.addBindValue(settings.motivationalInterval);
    query.addBindValue(settings.userName);
    query.addBindValue(settings.goal);
    
    if (!query.exec()) {
        qCritical() << "保存用户设置失败:" << query.lastError().text();
        return false;
    }
    
    return true;
}

UserSettings DataManager::loadUserSettings()
{
    UserSettings settings;
    
    QSqlQuery query("SELECT * FROM user_settings WHERE id = 1");
    if (query.next()) {
        settings.startDate = QDate::fromString(query.value("start_date").toString(), Qt::ISODate);
        settings.dailyReminderTime = QTime::fromString(query.value("daily_reminder_time").toString());
        
        // 解析危险时段JSON
        QJsonDocument doc = QJsonDocument::fromJson(query.value("danger_times").toByteArray());
        QJsonArray array = doc.array();
        for (const QJsonValue& value : array) {
            settings.dangerTimes.append(QTime::fromString(value.toString()));
        }
        
        settings.motivationalEnabled = query.value("motivational_enabled").toBool();
        settings.motivationalInterval = query.value("motivational_interval").toInt();
        settings.userName = query.value("user_name").toString();
        settings.goal = query.value("goal").toString();
    } else {
        // 返回默认设置
        settings.startDate = QDate::currentDate();
        settings.dailyReminderTime = QTime(21, 0); // 晚上9点
        settings.motivationalEnabled = true;
        settings.motivationalInterval = 2;
        settings.userName = "用户";
        settings.goal = "戒除不良习惯，重获新生";
    }
    
    return settings;
}

void DataManager::insertDefaultAchievements()
{
    QList<QPair<QString, QString>> achievements = {
        {"新的开始", "开始你的戒断之旅"},
        {"坚持一天", "成功戒断1天"},
        {"一周里程碑", "成功戒断7天"},
        {"月度成就", "成功戒断30天"},
        {"季度英雄", "成功戒断90天"},
        {"年度传奇", "成功戒断365天"},
        {"情绪管理师", "连续7天记录心情"},
        {"自我反思者", "写下50篇日记"},
        {"意志坚定", "抵御100次冲动"}
    };
    
    for (const auto& achievement : achievements) {
        QSqlQuery query;
        query.prepare("INSERT OR IGNORE INTO achievements (name, description) VALUES (?, ?)");
        query.addBindValue(achievement.first);
        query.addBindValue(achievement.second);
        query.exec();
    }
}
