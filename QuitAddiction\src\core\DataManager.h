#ifndef DATAMANAGER_H
#define DATAMANAGER_H

#include <QObject>
#include <QSqlDatabase>
#include <QDate>
#include <QTime>
#include <QStringList>

struct DailyRecord {
    QDate date;
    int moodScore;          // 心情评分 1-10
    QString notes;          // 日记内容
    bool hadUrge;          // 是否有冲动
    int urgeIntensity;     // 冲动强度 1-10
    QStringList triggers;   // 触发因素
    QStringList activities; // 替代活动
    bool isSuccess;        // 当日是否成功
};

struct UserSettings {
    QDate startDate;           // 开始戒断日期
    QTime dailyReminderTime;   // 每日提醒时间
    QList<QTime> dangerTimes;  // 危险时段
    bool motivationalEnabled;   // 是否启用励志提醒
    int motivationalInterval;   // 励志提醒间隔(小时)
    QString userName;          // 用户名
    QString goal;             // 戒断目标
};

class DataManager : public QObject
{
    Q_OBJECT

public:
    explicit DataManager(QObject *parent = nullptr);
    ~DataManager();

    // 数据库操作
    bool initialize();
    void close();

    // 用户设置
    bool saveUserSettings(const UserSettings& settings);
    UserSettings loadUserSettings();

    // 日记记录
    bool saveDailyRecord(const DailyRecord& record);
    DailyRecord loadDailyRecord(const QDate& date);
    QList<DailyRecord> loadRecordsInRange(const QDate& startDate, const QDate& endDate);

    // 统计数据
    int getCurrentStreak();           // 当前连续天数
    int getLongestStreak();          // 最长连续天数
    int getTotalDays();              // 总天数
    int getSuccessfulDays();         // 成功天数
    double getSuccessRate();         // 成功率
    QList<int> getMoodTrend(int days = 30);  // 心情趋势

    // 数据导出/导入
    bool exportData(const QString& filePath);
    bool importData(const QString& filePath);

signals:
    void dataChanged();
    void recordSaved(const QDate& date);

private:
    bool createTables();
    bool executeSql(const QString& sql);
    
private:
    QSqlDatabase m_database;
    QString m_databasePath;
};

#endif // DATAMANAGER_H
