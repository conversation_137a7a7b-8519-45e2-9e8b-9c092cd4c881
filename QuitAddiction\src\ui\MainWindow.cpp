#include "MainWindow.h"
#include "core/DataManager.h"
#include "utils/NotificationManager.h"
#include "widgets/StatisticsWidget.h"
#include "widgets/SettingsWidget.h"

#include <QApplication>
#include <QCloseEvent>
#include <QMenuBar>
#include <QStatusBar>
#include <QMessageBox>
#include <QSystemTrayIcon>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QSplitter>
#include <QGroupBox>
#include <QFrame>
#include <QRandomGenerator>
#include <QTimer>
#include <QScreen>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_dataManager(nullptr)
    , m_notificationManager(nullptr)
    , m_tabWidget(nullptr)
    , m_dashboardWidget(nullptr)
    , m_diaryWidget(nullptr)
    , m_statisticsWidget(nullptr)
    , m_settingsWidget(nullptr)
    , m_statusLabel(nullptr)
    , m_streakLabel(nullptr)
    , m_minimizeToTray(true)
    , m_startMinimized(false)
{
    // 初始化数据管理器
    m_dataManager = new DataManager(this);
    if (!m_dataManager->initialize()) {
        QMessageBox::critical(this, "错误", "无法初始化数据库！");
        QApplication::quit();
        return;
    }

    // 初始化通知管理器
    m_notificationManager = new NotificationManager(this);
    m_notificationManager->initialize();

    setupUI();
    setupMenuBar();
    setupStatusBar();
    connectSignals();
    loadSettings();

    // 设置窗口属性
    setWindowTitle("戒瘾助手 - QuitAddiction");
    setWindowIcon(QIcon(":/resources/icons/app_icon.png"));
    resize(1000, 700);
    
    // 居中显示
    move(QApplication::primaryScreen()->geometry().center() - rect().center());
}

MainWindow::~MainWindow()
{
    saveSettings();
}

void MainWindow::setupUI()
{
    // 创建中央部件
    QWidget* centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);

    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(centralWidget);
    mainLayout->setContentsMargins(10, 10, 10, 10);

    // 创建标签页控件
    m_tabWidget = new QTabWidget(this);
    m_tabWidget->setTabPosition(QTabWidget::North);
    m_tabWidget->setMovable(false);

    // 创建各个页面
    m_dashboardWidget = new DashboardWidget(m_dataManager, this);
    m_diaryWidget = new DiaryWidget(m_dataManager, this);
    m_statisticsWidget = new StatisticsWidget(m_dataManager, this);
    m_settingsWidget = new SettingsWidget(m_dataManager, this);

    // 添加标签页
    m_tabWidget->addTab(m_dashboardWidget, QIcon(":/resources/icons/dashboard.png"), "仪表盘");
    m_tabWidget->addTab(m_diaryWidget, QIcon(":/resources/icons/diary.png"), "日记");
    m_tabWidget->addTab(m_statisticsWidget, QIcon(":/resources/icons/statistics.png"), "统计");
    m_tabWidget->addTab(m_settingsWidget, QIcon(":/resources/icons/settings.png"), "设置");

    mainLayout->addWidget(m_tabWidget);
}

void MainWindow::setupMenuBar()
{
    // 文件菜单
    QMenu* fileMenu = menuBar()->addMenu("文件(&F)");
    
    QAction* exportAction = fileMenu->addAction("导出数据(&E)");
    exportAction->setShortcut(QKeySequence::SaveAs);
    
    QAction* importAction = fileMenu->addAction("导入数据(&I)");
    importAction->setShortcut(QKeySequence::Open);
    
    fileMenu->addSeparator();
    
    QAction* exitAction = fileMenu->addAction("退出(&X)");
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);

    // 工具菜单
    QMenu* toolsMenu = menuBar()->addMenu("工具(&T)");
    
    QAction* emergencyAction = toolsMenu->addAction("紧急求助(&E)");
    emergencyAction->setShortcut(QKeySequence("F1"));
    connect(emergencyAction, &QAction::triggered, this, &MainWindow::onEmergencyHelp);
    
    // 帮助菜单
    QMenu* helpMenu = menuBar()->addMenu("帮助(&H)");
    
    QAction* aboutAction = helpMenu->addAction("关于(&A)");
    connect(aboutAction, &QAction::triggered, [this]() {
        QMessageBox::about(this, "关于", 
            "戒瘾助手 v1.0.0\n\n"
            "一个帮助您戒除不良习惯的辅助工具。\n"
            "坚持就是胜利！");
    });
}

void MainWindow::setupStatusBar()
{
    m_statusLabel = new QLabel("就绪", this);
    m_streakLabel = new QLabel("连续天数: 0", this);
    
    statusBar()->addWidget(m_statusLabel);
    statusBar()->addPermanentWidget(m_streakLabel);
    
    // 更新状态栏
    int streak = m_dataManager->getCurrentStreak();
    m_streakLabel->setText(QString("连续天数: %1").arg(streak));
}

void MainWindow::connectSignals()
{
    // 通知管理器信号
    connect(m_notificationManager, &NotificationManager::showMainWindow,
            this, &MainWindow::showFromTray);
    connect(m_notificationManager, &NotificationManager::emergencyHelpRequested,
            this, &MainWindow::onEmergencyHelp);

    // 标签页切换
    connect(m_tabWidget, &QTabWidget::currentChanged,
            this, &MainWindow::onTabChanged);

    // 数据变化信号
    connect(m_dataManager, &DataManager::dataChanged, [this]() {
        m_dashboardWidget->updateDisplay();
        m_statisticsWidget->updateCharts();
        
        int streak = m_dataManager->getCurrentStreak();
        m_streakLabel->setText(QString("连续天数: %1").arg(streak));
    });
}

void MainWindow::loadSettings()
{
    // 这里可以加载窗口设置
    // 比如窗口大小、位置等
}

void MainWindow::saveSettings()
{
    // 这里可以保存窗口设置
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    if (m_minimizeToTray && QSystemTrayIcon::isSystemTrayAvailable()) {
        hide();
        event->ignore();
        
        if (!property("trayMessageShown").toBool()) {
            QSystemTrayIcon* trayIcon = m_notificationManager->findChild<QSystemTrayIcon*>();
            if (trayIcon) {
                trayIcon->showMessage("戒瘾助手", "程序已最小化到系统托盘", 
                                    QSystemTrayIcon::Information, 2000);
            }
            setProperty("trayMessageShown", true);
        }
    } else {
        event->accept();
    }
}

void MainWindow::changeEvent(QEvent *event)
{
    if (event->type() == QEvent::WindowStateChange) {
        if (isMinimized() && m_minimizeToTray) {
            hide();
            event->ignore();
            return;
        }
    }
    QMainWindow::changeEvent(event);
}

void MainWindow::showFromTray()
{
    show();
    raise();
    activateWindow();
    
    if (isMinimized()) {
        showNormal();
    }
}

void MainWindow::onEmergencyHelp()
{
    // 显示紧急求助对话框
    QMessageBox msgBox(this);
    msgBox.setWindowTitle("紧急求助");
    msgBox.setIcon(QMessageBox::Information);
    msgBox.setText("请记住：这只是暂时的感觉。");
    msgBox.setInformativeText("深呼吸，这种冲动会过去的。\n\n你可以尝试：\n• 深呼吸5分钟\n• 出去散步\n• 联系支持者\n• 进行冥想\n• 做其他健康活动");
    
    QPushButton* meditateBtn = msgBox.addButton("开始冥想", QMessageBox::ActionRole);
    QPushButton* walkBtn = msgBox.addButton("出去走走", QMessageBox::ActionRole);
    QPushButton* okBtn = msgBox.addButton("我知道了", QMessageBox::AcceptRole);
    
    msgBox.exec();
    
    if (msgBox.clickedButton() == meditateBtn) {
        // 这里可以启动冥想功能
        m_statusLabel->setText("建议进行5分钟冥想...");
    } else if (msgBox.clickedButton() == walkBtn) {
        m_statusLabel->setText("建议出去散步15分钟...");
    }
}

void MainWindow::onTabChanged(int index)
{
    // 根据切换的标签页更新状态
    QStringList tabNames = {"仪表盘", "日记", "统计", "设置"};
    if (index >= 0 && index < tabNames.size()) {
        m_statusLabel->setText(QString("当前页面: %1").arg(tabNames[index]));
    }
    
    // 更新对应页面的数据
    switch (index) {
    case 0: // 仪表盘
        m_dashboardWidget->updateDisplay();
        break;
    case 1: // 日记
        m_diaryWidget->loadTodayRecord();
        break;
    case 2: // 统计
        m_statisticsWidget->updateCharts();
        break;
    case 3: // 设置
        m_settingsWidget->loadSettings();
        break;
    }
}

// DashboardWidget 实现
DashboardWidget::DashboardWidget(DataManager* dataManager, QWidget *parent)
    : QWidget(parent)
    , m_dataManager(dataManager)
    , m_streakLabel(nullptr)
    , m_totalDaysLabel(nullptr)
    , m_successRateLabel(nullptr)
    , m_motivationalLabel(nullptr)
    , m_emergencyButton(nullptr)
    , m_quickNoteButton(nullptr)
    , m_dailyProgressBar(nullptr)
{
    setupUI();
    updateDisplay();
}

void DashboardWidget::setupUI()
{
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(20);
    mainLayout->setContentsMargins(20, 20, 20, 20);

    // 欢迎区域
    QGroupBox* welcomeGroup = new QGroupBox("今日概览", this);
    QGridLayout* welcomeLayout = new QGridLayout(welcomeGroup);

    m_streakLabel = new QLabel("连续天数: 0", this);
    m_streakLabel->setStyleSheet("font-size: 24px; font-weight: bold; color: #2E8B57;");

    m_totalDaysLabel = new QLabel("总天数: 0", this);
    m_totalDaysLabel->setStyleSheet("font-size: 16px; color: #4682B4;");

    m_successRateLabel = new QLabel("成功率: 0%", this);
    m_successRateLabel->setStyleSheet("font-size: 16px; color: #FF6347;");

    welcomeLayout->addWidget(m_streakLabel, 0, 0, 1, 2);
    welcomeLayout->addWidget(m_totalDaysLabel, 1, 0);
    welcomeLayout->addWidget(m_successRateLabel, 1, 1);

    mainLayout->addWidget(welcomeGroup);

    // 励志消息
    QGroupBox* motivationGroup = new QGroupBox("今日励志", this);
    QVBoxLayout* motivationLayout = new QVBoxLayout(motivationGroup);

    m_motivationalLabel = new QLabel("每一天的坚持都让你更强大！", this);
    m_motivationalLabel->setWordWrap(true);
    m_motivationalLabel->setStyleSheet("font-size: 14px; font-style: italic; padding: 10px;");
    m_motivationalLabel->setAlignment(Qt::AlignCenter);

    motivationLayout->addWidget(m_motivationalLabel);
    mainLayout->addWidget(motivationGroup);

    // 快捷操作
    QGroupBox* actionGroup = new QGroupBox("快捷操作", this);
    QHBoxLayout* actionLayout = new QHBoxLayout(actionGroup);

    m_emergencyButton = new QPushButton("紧急求助", this);
    m_emergencyButton->setStyleSheet("QPushButton { background-color: #FF4444; color: white; font-weight: bold; padding: 10px; }");

    m_quickNoteButton = new QPushButton("快速记录", this);
    m_quickNoteButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }");

    actionLayout->addWidget(m_emergencyButton);
    actionLayout->addWidget(m_quickNoteButton);

    mainLayout->addWidget(actionGroup);

    // 连接信号
    connect(m_emergencyButton, &QPushButton::clicked, this, &DashboardWidget::onEmergencyButtonClicked);
    connect(m_quickNoteButton, &QPushButton::clicked, this, &DashboardWidget::onQuickNoteClicked);

    mainLayout->addStretch();
}

void DashboardWidget::updateDisplay()
{
    if (!m_dataManager) return;

    updateStreakDisplay();
    updateMotivationalMessage();
}

void DashboardWidget::updateStreakDisplay()
{
    int currentStreak = m_dataManager->getCurrentStreak();
    int totalDays = m_dataManager->getTotalDays();
    double successRate = m_dataManager->getSuccessRate();

    m_streakLabel->setText(QString("连续天数: %1").arg(currentStreak));
    m_totalDaysLabel->setText(QString("总天数: %1").arg(totalDays));
    m_successRateLabel->setText(QString("成功率: %1%").arg(QString::number(successRate, 'f', 1)));
}

void DashboardWidget::updateMotivationalMessage()
{
    QStringList messages = {
        "每一天的坚持都让你更强大！",
        "相信自己，你正在创造奇迹！",
        "今天也要加油哦！",
        "记住你的目标，为什么要开始这个旅程？",
        "困难只是暂时的，但你的成长是永久的！",
        "你已经走了这么远，不要放弃！",
        "每个选择都在塑造更好的你！"
    };

    int index = QRandomGenerator::global()->bounded(messages.size());
    m_motivationalLabel->setText(messages[index]);
}

void DashboardWidget::onEmergencyButtonClicked()
{
    // 发送紧急求助信号到主窗口
    if (QMainWindow* mainWindow = qobject_cast<QMainWindow*>(window())) {
        QMetaObject::invokeMethod(mainWindow, "onEmergencyHelp");
    }
}

void DashboardWidget::onQuickNoteClicked()
{
    // 切换到日记页面
    if (QTabWidget* tabWidget = findChild<QTabWidget*>()) {
        tabWidget->setCurrentIndex(1); // 日记页面
    }
}

// DiaryWidget 实现
DiaryWidget::DiaryWidget(DataManager* dataManager, QWidget *parent)
    : QWidget(parent)
    , m_dataManager(dataManager)
{
    setupUI();
    loadTodayRecord();
}

void DiaryWidget::setupUI()
{
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(20, 20, 20, 20);

    // 日期选择
    QHBoxLayout* dateLayout = new QHBoxLayout();
    dateLayout->addWidget(new QLabel("日期:", this));

    m_dateEdit = new QDateEdit(QDate::currentDate(), this);
    m_dateEdit->setCalendarPopup(true);
    dateLayout->addWidget(m_dateEdit);
    dateLayout->addStretch();

    mainLayout->addLayout(dateLayout);

    // 心情评分
    QGroupBox* moodGroup = new QGroupBox("心情评分", this);
    QVBoxLayout* moodLayout = new QVBoxLayout(moodGroup);

    m_moodSlider = new QSlider(Qt::Horizontal, this);
    m_moodSlider->setRange(1, 10);
    m_moodSlider->setValue(5);

    m_moodLabel = new QLabel("心情: 5/10", this);

    moodLayout->addWidget(m_moodLabel);
    moodLayout->addWidget(m_moodSlider);

    mainLayout->addWidget(moodGroup);

    // 日记内容
    QGroupBox* notesGroup = new QGroupBox("今日感受", this);
    QVBoxLayout* notesLayout = new QVBoxLayout(notesGroup);

    m_notesEdit = new QTextEdit(this);
    m_notesEdit->setPlaceholderText("记录今天的感受、想法或发生的事情...");

    notesLayout->addWidget(m_notesEdit);
    mainLayout->addWidget(notesGroup);

    // 冲动强度
    QGroupBox* urgeGroup = new QGroupBox("冲动强度", this);
    QVBoxLayout* urgeLayout = new QVBoxLayout(urgeGroup);

    m_urgeSlider = new QSlider(Qt::Horizontal, this);
    m_urgeSlider->setRange(0, 10);
    m_urgeSlider->setValue(0);

    m_urgeLabel = new QLabel("冲动强度: 0/10", this);

    urgeLayout->addWidget(m_urgeLabel);
    urgeLayout->addWidget(m_urgeSlider);

    mainLayout->addWidget(urgeGroup);

    // 保存按钮
    m_saveButton = new QPushButton("保存记录", this);
    m_saveButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }");

    mainLayout->addWidget(m_saveButton);

    // 连接信号
    connect(m_dateEdit, &QDateEdit::dateChanged, this, &DiaryWidget::onDateChanged);
    connect(m_moodSlider, &QSlider::valueChanged, this, &DiaryWidget::onMoodChanged);
    connect(m_urgeSlider, &QSlider::valueChanged, [this](int value) {
        m_urgeLabel->setText(QString("冲动强度: %1/10").arg(value));
    });
    connect(m_saveButton, &QPushButton::clicked, this, &DiaryWidget::onSaveClicked);
}

void DiaryWidget::loadTodayRecord()
{
    loadRecord(QDate::currentDate());
}

void DiaryWidget::loadRecord(const QDate& date)
{
    if (!m_dataManager) return;

    DailyRecord record = m_dataManager->loadDailyRecord(date);

    m_dateEdit->setDate(date);
    m_moodSlider->setValue(record.moodScore > 0 ? record.moodScore : 5);
    m_notesEdit->setPlainText(record.notes);
    m_urgeSlider->setValue(record.urgeIntensity);

    onMoodChanged(m_moodSlider->value());
    m_urgeLabel->setText(QString("冲动强度: %1/10").arg(record.urgeIntensity));
}

void DiaryWidget::onDateChanged()
{
    loadRecord(m_dateEdit->date());
}

void DiaryWidget::onMoodChanged(int value)
{
    m_moodLabel->setText(QString("心情: %1/10").arg(value));
}

void DiaryWidget::onSaveClicked()
{
    saveCurrentRecord();
}

void DiaryWidget::saveCurrentRecord()
{
    if (!m_dataManager) return;

    DailyRecord record;
    record.date = m_dateEdit->date();
    record.moodScore = m_moodSlider->value();
    record.notes = m_notesEdit->toPlainText();
    record.hadUrge = m_urgeSlider->value() > 0;
    record.urgeIntensity = m_urgeSlider->value();
    record.isSuccess = m_urgeSlider->value() < 5; // 简单的成功判断逻辑

    if (m_dataManager->saveDailyRecord(record)) {
        m_saveButton->setText("保存成功！");
        QTimer::singleShot(2000, [this]() {
            m_saveButton->setText("保存记录");
        });
    } else {
        m_saveButton->setText("保存失败");
        QTimer::singleShot(2000, [this]() {
            m_saveButton->setText("保存记录");
        });
    }
}
