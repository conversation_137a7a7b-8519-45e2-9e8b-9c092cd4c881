#ifndef STATISTICSWIDGET_H
#define STATISTICSWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QComboBox>
#include <QPushButton>
#include <QDateEdit>

QT_BEGIN_NAMESPACE
class QChartView;
class QChart;
class QLineSeries;
class QBarSeries;
class QBarSet;
class QDateTimeAxis;
class QValueAxis;
QT_END_NAMESPACE

class DataManager;

class StatisticsWidget : public QWidget
{
    Q_OBJECT

public:
    explicit StatisticsWidget(DataManager* dataManager, QWidget *parent = nullptr);

public slots:
    void updateCharts();

private slots:
    void onChartTypeChanged();
    void onTimeRangeChanged();
    void onExportClicked();

private:
    void setupUI();
    void createMoodChart();
    void createStreakChart();
    void createSuccessRateChart();
    void createUrgeIntensityChart();
    void updateStatisticsLabels();

private:
    DataManager* m_dataManager;
    
    // UI组件
    QComboBox* m_chartTypeCombo;
    QComboBox* m_timeRangeCombo;
    QDateEdit* m_startDateEdit;
    QDateEdit* m_endDateEdit;
    QPushButton* m_exportButton;
    
    // 图表组件
    QChartView* m_chartView;
    QChart* m_chart;
    
    // 统计标签
    QLabel* m_currentStreakLabel;
    QLabel* m_longestStreakLabel;
    QLabel* m_totalDaysLabel;
    QLabel* m_successfulDaysLabel;
    QLabel* m_successRateLabel;
    QLabel* m_averageMoodLabel;
    
    // 图表数据
    QLineSeries* m_moodSeries;
    QLineSeries* m_urgeSeries;
    QBarSeries* m_successSeries;
    
    // 坐标轴
    QDateTimeAxis* m_axisX;
    QValueAxis* m_axisY;
};

// 成就系统组件
class AchievementWidget : public QWidget
{
    Q_OBJECT

public:
    explicit AchievementWidget(DataManager* dataManager, QWidget *parent = nullptr);

public slots:
    void checkAchievements();

private:
    void setupUI();
    void addAchievement(const QString& title, const QString& description, 
                       bool unlocked, const QString& iconPath = QString());

private:
    DataManager* m_dataManager;
    QVBoxLayout* m_achievementsLayout;
    
    struct Achievement {
        QString title;
        QString description;
        bool unlocked;
        QString iconPath;
        int requirement;
    };
    
    QList<Achievement> m_achievements;
};

// 进度环形图组件
class ProgressRingWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ProgressRingWidget(QWidget *parent = nullptr);
    
    void setValue(int value);
    void setMaxValue(int maxValue);
    void setTitle(const QString& title);
    void setColor(const QColor& color);

protected:
    void paintEvent(QPaintEvent *event) override;

private:
    int m_value;
    int m_maxValue;
    QString m_title;
    QColor m_color;
    int m_lineWidth;
};

#endif // STATISTICSWIDGET_H
