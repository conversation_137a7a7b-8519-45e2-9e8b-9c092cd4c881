# QuitAddiction - 戒瘾助手

一个基于Qt6开发的戒瘾辅助软件，帮助用户戒除不良习惯，建立健康的生活方式。

## 功能特性

### 🎯 核心功能
- **戒断天数追踪**：实时显示连续戒断天数和总体进度
- **情绪日记**：记录每日心情、触发因素和应对策略
- **智能提醒**：自定义时间提醒和危险时段警报
- **数据统计**：详细的图表分析和进度可视化
- **成就系统**：里程碑奖励激励持续进步

### 🛡️ 防护机制
- **紧急求助**：一键获取支持和应对策略
- **危险时段提醒**：在易发生冲动的时间段发出警告
- **替代活动建议**：推荐健康的替代行为

### 📊 数据分析
- **心情趋势图**：追踪情绪变化模式
- **成功率统计**：分析戒断效果
- **冲动强度分析**：了解触发因素和应对效果
- **长期趋势**：多维度数据可视化

### 🎨 用户体验
- **现代化界面**：简洁美观的用户界面
- **深色/浅色主题**：护眼模式支持
- **系统托盘集成**：后台运行，隐私保护
- **多语言支持**：中英文界面

## 技术架构

### 开发环境
- **框架**：Qt 6.x
- **语言**：C++17
- **构建系统**：CMake
- **数据库**：SQLite
- **图表库**：Qt Charts

### 项目结构
```
QuitAddiction/
├── src/                    # 源代码目录
│   ├── core/              # 核心业务逻辑
│   │   ├── DataManager.h/cpp      # 数据管理
│   │   └── ...
│   ├── ui/                # 主界面
│   │   ├── MainWindow.h/cpp       # 主窗口
│   │   └── ...
│   ├── widgets/           # 自定义控件
│   │   ├── StatisticsWidget.h     # 统计图表
│   │   ├── SettingsWidget.h       # 设置页面
│   │   └── ...
│   ├── utils/             # 工具类
│   │   ├── NotificationManager.h/cpp  # 通知管理
│   │   └── ...
│   └── main.cpp           # 程序入口
├── resources/             # 资源文件
│   ├── icons/            # 图标文件
│   ├── audio/            # 音频文件
│   ├── styles/           # 样式表
│   └── resources.qrc     # 资源配置
├── CMakeLists.txt        # CMake配置
└── README.md            # 项目说明
```

## 编译构建

### 环境要求
- Qt 6.2 或更高版本
- CMake 3.16 或更高版本
- C++17 兼容编译器
- SQLite 支持

### 构建步骤

#### Windows (Visual Studio)
```bash
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022"
cmake --build . --config Release
```

#### Windows (MinGW)
```bash
mkdir build
cd build
cmake .. -G "MinGW Makefiles"
cmake --build .
```

#### Linux/macOS
```bash
mkdir build
cd build
cmake ..
make -j4
```

### 依赖库
项目使用以下Qt模块：
- Qt6::Core - 核心功能
- Qt6::Widgets - 界面组件
- Qt6::Sql - 数据库支持
- Qt6::Charts - 图表功能
- Qt6::Multimedia - 音频播放
- Qt6::Network - 网络功能

## 使用说明

### 首次运行
1. 设置用户名和戒断目标
2. 选择开始日期
3. 配置提醒时间和危险时段
4. 开始记录每日情况

### 日常使用
1. **记录心情**：每日记录心情评分和感受
2. **应对冲动**：使用紧急求助功能
3. **查看进度**：在统计页面查看进展
4. **调整设置**：根据需要修改提醒和配置

### 数据管理
- **自动备份**：程序自动备份重要数据
- **导出功能**：支持导出数据到文件
- **隐私保护**：所有数据本地存储，不上传云端

## 开发计划

### 已完成功能
- [x] 基础项目架构
- [x] 数据存储系统
- [x] 通知管理器
- [x] 主界面框架

### 开发中功能
- [ ] 主窗口实现
- [ ] 统计图表功能
- [ ] 设置页面
- [ ] 成就系统

### 计划功能
- [ ] 冥想计时器
- [ ] 网站屏蔽器
- [ ] 数据同步
- [ ] 移动端支持

## 贡献指南

欢迎贡献代码和建议！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 联系方式

- 项目主页：[GitHub Repository]
- 问题反馈：[Issues]
- 邮箱：[<EMAIL>]

---

**免责声明**：本软件仅作为辅助工具，不能替代专业的医疗建议。如有严重成瘾问题，请寻求专业帮助。
