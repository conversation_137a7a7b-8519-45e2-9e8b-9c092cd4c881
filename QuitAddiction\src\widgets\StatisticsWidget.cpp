#include "StatisticsWidget.h"
#include "core/DataManager.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QComboBox>
#include <QPushButton>
#include <QDateEdit>
#include <QFileDialog>
#include <QMessageBox>
#include <QStandardPaths>

// 如果没有Qt Charts，使用简单的替代实现
#ifndef QT_CHARTS_LIB
class QChartView : public QWidget {
public:
    QChartView(QWidget* parent = nullptr) : QWidget(parent) {
        setMinimumSize(400, 300);
        setStyleSheet("background-color: #f0f0f0; border: 1px solid #ccc;");
    }
};
class QChart {};
class QLineSeries {};
class QBarSeries {};
class QBarSet {};
class QDateTimeAxis {};
class QValueAxis {};
#endif

StatisticsWidget::StatisticsWidget(DataManager* dataManager, QWidget *parent)
    : QWidget(parent)
    , m_dataManager(dataManager)
    , m_chartTypeCombo(nullptr)
    , m_timeRangeCombo(nullptr)
    , m_startDateEdit(nullptr)
    , m_endDateEdit(nullptr)
    , m_exportButton(nullptr)
    , m_chartView(nullptr)
    , m_chart(nullptr)
    , m_currentStreakLabel(nullptr)
    , m_longestStreakLabel(nullptr)
    , m_totalDaysLabel(nullptr)
    , m_successfulDaysLabel(nullptr)
    , m_successRateLabel(nullptr)
    , m_averageMoodLabel(nullptr)
    , m_moodSeries(nullptr)
    , m_urgeSeries(nullptr)
    , m_successSeries(nullptr)
    , m_axisX(nullptr)
    , m_axisY(nullptr)
{
    setupUI();
    updateCharts();
}

void StatisticsWidget::setupUI()
{
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(20, 20, 20, 20);
    mainLayout->setSpacing(15);

    // 控制面板
    QGroupBox* controlGroup = new QGroupBox("图表控制", this);
    QHBoxLayout* controlLayout = new QHBoxLayout(controlGroup);
    
    controlLayout->addWidget(new QLabel("图表类型:", this));
    m_chartTypeCombo = new QComboBox(this);
    m_chartTypeCombo->addItems({"心情趋势", "成功率统计", "冲动强度分析"});
    controlLayout->addWidget(m_chartTypeCombo);
    
    controlLayout->addWidget(new QLabel("时间范围:", this));
    m_timeRangeCombo = new QComboBox(this);
    m_timeRangeCombo->addItems({"最近7天", "最近30天", "最近90天", "自定义"});
    controlLayout->addWidget(m_timeRangeCombo);
    
    m_startDateEdit = new QDateEdit(QDate::currentDate().addDays(-30), this);
    m_endDateEdit = new QDateEdit(QDate::currentDate(), this);
    m_startDateEdit->setVisible(false);
    m_endDateEdit->setVisible(false);
    
    controlLayout->addWidget(m_startDateEdit);
    controlLayout->addWidget(m_endDateEdit);
    
    m_exportButton = new QPushButton("导出图表", this);
    controlLayout->addWidget(m_exportButton);
    
    controlLayout->addStretch();
    mainLayout->addWidget(controlGroup);

    // 统计数据面板
    QGroupBox* statsGroup = new QGroupBox("统计概览", this);
    QGridLayout* statsLayout = new QGridLayout(statsGroup);
    
    m_currentStreakLabel = new QLabel("当前连续: 0天", this);
    m_longestStreakLabel = new QLabel("最长连续: 0天", this);
    m_totalDaysLabel = new QLabel("总天数: 0天", this);
    m_successfulDaysLabel = new QLabel("成功天数: 0天", this);
    m_successRateLabel = new QLabel("成功率: 0%", this);
    m_averageMoodLabel = new QLabel("平均心情: 0/10", this);
    
    // 设置标签样式
    QStringList labels = {
        m_currentStreakLabel->text(), m_longestStreakLabel->text(),
        m_totalDaysLabel->text(), m_successfulDaysLabel->text(),
        m_successRateLabel->text(), m_averageMoodLabel->text()
    };
    
    QList<QLabel*> labelWidgets = {
        m_currentStreakLabel, m_longestStreakLabel,
        m_totalDaysLabel, m_successfulDaysLabel,
        m_successRateLabel, m_averageMoodLabel
    };
    
    for (QLabel* label : labelWidgets) {
        label->setStyleSheet("font-size: 14px; font-weight: bold; padding: 5px;");
    }
    
    statsLayout->addWidget(m_currentStreakLabel, 0, 0);
    statsLayout->addWidget(m_longestStreakLabel, 0, 1);
    statsLayout->addWidget(m_totalDaysLabel, 1, 0);
    statsLayout->addWidget(m_successfulDaysLabel, 1, 1);
    statsLayout->addWidget(m_successRateLabel, 2, 0);
    statsLayout->addWidget(m_averageMoodLabel, 2, 1);
    
    mainLayout->addWidget(statsGroup);

    // 图表区域
    QGroupBox* chartGroup = new QGroupBox("数据图表", this);
    QVBoxLayout* chartLayout = new QVBoxLayout(chartGroup);
    
    m_chartView = new QChartView(this);
    chartLayout->addWidget(m_chartView);
    
    mainLayout->addWidget(chartGroup);

    // 连接信号
    connect(m_chartTypeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &StatisticsWidget::onChartTypeChanged);
    connect(m_timeRangeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &StatisticsWidget::onTimeRangeChanged);
    connect(m_exportButton, &QPushButton::clicked, this, &StatisticsWidget::onExportClicked);
}

void StatisticsWidget::updateCharts()
{
    updateStatisticsLabels();
    
    // 根据选择的图表类型更新图表
    int chartType = m_chartTypeCombo ? m_chartTypeCombo->currentIndex() : 0;
    
    switch (chartType) {
    case 0:
        createMoodChart();
        break;
    case 1:
        createSuccessRateChart();
        break;
    case 2:
        createUrgeIntensityChart();
        break;
    default:
        createMoodChart();
        break;
    }
}

void StatisticsWidget::updateStatisticsLabels()
{
    if (!m_dataManager) return;
    
    int currentStreak = m_dataManager->getCurrentStreak();
    int longestStreak = m_dataManager->getLongestStreak();
    int totalDays = m_dataManager->getTotalDays();
    int successfulDays = m_dataManager->getSuccessfulDays();
    double successRate = m_dataManager->getSuccessRate();
    
    // 计算平均心情
    QList<int> moodTrend = m_dataManager->getMoodTrend(30);
    double averageMood = 0.0;
    if (!moodTrend.isEmpty()) {
        int sum = 0;
        for (int mood : moodTrend) {
            sum += mood;
        }
        averageMood = static_cast<double>(sum) / moodTrend.size();
    }
    
    m_currentStreakLabel->setText(QString("当前连续: %1天").arg(currentStreak));
    m_longestStreakLabel->setText(QString("最长连续: %1天").arg(longestStreak));
    m_totalDaysLabel->setText(QString("总天数: %1天").arg(totalDays));
    m_successfulDaysLabel->setText(QString("成功天数: %1天").arg(successfulDays));
    m_successRateLabel->setText(QString("成功率: %1%").arg(QString::number(successRate, 'f', 1)));
    m_averageMoodLabel->setText(QString("平均心情: %1/10").arg(QString::number(averageMood, 'f', 1)));
}

void StatisticsWidget::createMoodChart()
{
    // 简单的图表占位符实现
    if (m_chartView) {
        m_chartView->setToolTip("心情趋势图 - 显示最近30天的心情变化");
    }
}

void StatisticsWidget::createSuccessRateChart()
{
    if (m_chartView) {
        m_chartView->setToolTip("成功率统计图 - 显示每周的成功率变化");
    }
}

void StatisticsWidget::createUrgeIntensityChart()
{
    if (m_chartView) {
        m_chartView->setToolTip("冲动强度分析图 - 显示冲动强度的变化趋势");
    }
}

void StatisticsWidget::onChartTypeChanged()
{
    updateCharts();
}

void StatisticsWidget::onTimeRangeChanged()
{
    int index = m_timeRangeCombo->currentIndex();
    bool isCustom = (index == 3);
    
    m_startDateEdit->setVisible(isCustom);
    m_endDateEdit->setVisible(isCustom);
    
    updateCharts();
}

void StatisticsWidget::onExportClicked()
{
    QString fileName = QFileDialog::getSaveFileName(this,
        "导出统计数据", 
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/statistics.png",
        "PNG图片 (*.png);;PDF文件 (*.pdf)");
    
    if (!fileName.isEmpty()) {
        // 这里实现导出功能
        QMessageBox::information(this, "导出完成", "统计数据已导出到: " + fileName);
    }
}
