#include "SettingsWidget.h"
#include "core/DataManager.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QLineEdit>
#include <QTextEdit>
#include <QDateEdit>
#include <QTimeEdit>
#include <QCheckBox>
#include <QSpinBox>
#include <QComboBox>
#include <QPushButton>
#include <QListWidget>
#include <QSlider>
#include <QFileDialog>
#include <QMessageBox>
#include <QStandardPaths>
#include <QTime>

SettingsWidget::SettingsWidget(DataManager* dataManager, QWidget *parent)
    : QWidget(parent)
    , m_dataManager(dataManager)
{
    setupUI();
    loadSettings();
}

void SettingsWidget::setupUI()
{
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(20, 20, 20, 20);
    mainLayout->setSpacing(15);

    setupGeneralSettings();
    setupNotificationSettings();
    setupDangerTimeSettings();
    setupAppearanceSettings();
    setupDataSettings();

    // 按钮区域
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    
    m_saveBtn = new QPushButton("保存设置", this);
    m_saveBtn->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px 16px; }");
    
    m_cancelBtn = new QPushButton("取消", this);
    m_resetBtn = new QPushButton("恢复默认", this);
    
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_resetBtn);
    buttonLayout->addWidget(m_cancelBtn);
    buttonLayout->addWidget(m_saveBtn);
    
    mainLayout->addLayout(buttonLayout);
    
    connectSignals();
}

void SettingsWidget::setupGeneralSettings()
{
    QGroupBox* generalGroup = new QGroupBox("基本设置", this);
    QFormLayout* generalLayout = new QFormLayout(generalGroup);
    
    m_userNameEdit = new QLineEdit(this);
    m_userNameEdit->setPlaceholderText("请输入您的昵称");
    generalLayout->addRow("用户名:", m_userNameEdit);
    
    m_goalEdit = new QTextEdit(this);
    m_goalEdit->setMaximumHeight(80);
    m_goalEdit->setPlaceholderText("描述您的戒断目标...");
    generalLayout->addRow("戒断目标:", m_goalEdit);
    
    m_startDateEdit = new QDateEdit(QDate::currentDate(), this);
    m_startDateEdit->setCalendarPopup(true);
    generalLayout->addRow("开始日期:", m_startDateEdit);
    
    layout()->addWidget(generalGroup);
}

void SettingsWidget::setupNotificationSettings()
{
    QGroupBox* notificationGroup = new QGroupBox("通知设置", this);
    QFormLayout* notificationLayout = new QFormLayout(notificationGroup);
    
    // 每日提醒
    m_dailyReminderCheck = new QCheckBox("启用每日提醒", this);
    notificationLayout->addRow(m_dailyReminderCheck);
    
    m_dailyReminderTime = new QTimeEdit(QTime(21, 0), this);
    notificationLayout->addRow("提醒时间:", m_dailyReminderTime);
    
    // 励志消息
    m_motivationalCheck = new QCheckBox("启用励志消息", this);
    notificationLayout->addRow(m_motivationalCheck);
    
    m_motivationalInterval = new QSpinBox(this);
    m_motivationalInterval->setRange(1, 24);
    m_motivationalInterval->setValue(2);
    m_motivationalInterval->setSuffix(" 小时");
    notificationLayout->addRow("消息间隔:", m_motivationalInterval);
    
    layout()->addWidget(notificationGroup);
}

void SettingsWidget::setupDangerTimeSettings()
{
    QGroupBox* dangerGroup = new QGroupBox("危险时段设置", this);
    QVBoxLayout* dangerLayout = new QVBoxLayout(dangerGroup);
    
    QLabel* dangerLabel = new QLabel("设置容易产生冲动的时间段，系统会在这些时间发出提醒:", this);
    dangerLabel->setWordWrap(true);
    dangerLayout->addWidget(dangerLabel);
    
    // 危险时段列表
    m_dangerTimesList = new QListWidget(this);
    m_dangerTimesList->setMaximumHeight(120);
    dangerLayout->addWidget(m_dangerTimesList);
    
    // 添加新时段
    QHBoxLayout* addTimeLayout = new QHBoxLayout();
    
    m_newDangerTime = new QTimeEdit(QTime(22, 0), this);
    m_addDangerTimeBtn = new QPushButton("添加", this);
    m_removeDangerTimeBtn = new QPushButton("删除选中", this);
    
    addTimeLayout->addWidget(new QLabel("新时段:", this));
    addTimeLayout->addWidget(m_newDangerTime);
    addTimeLayout->addWidget(m_addDangerTimeBtn);
    addTimeLayout->addWidget(m_removeDangerTimeBtn);
    addTimeLayout->addStretch();
    
    dangerLayout->addLayout(addTimeLayout);
    
    layout()->addWidget(dangerGroup);
}

void SettingsWidget::setupAppearanceSettings()
{
    QGroupBox* appearanceGroup = new QGroupBox("外观设置", this);
    QFormLayout* appearanceLayout = new QFormLayout(appearanceGroup);
    
    // 主题选择
    m_themeCombo = new QComboBox(this);
    m_themeCombo->addItems({"浅色主题", "深色主题", "跟随系统"});
    appearanceLayout->addRow("主题:", m_themeCombo);
    
    // 语言选择
    m_languageCombo = new QComboBox(this);
    m_languageCombo->addItems({"简体中文", "English"});
    appearanceLayout->addRow("语言:", m_languageCombo);
    
    // 窗口透明度
    QHBoxLayout* opacityLayout = new QHBoxLayout();
    m_opacitySlider = new QSlider(Qt::Horizontal, this);
    m_opacitySlider->setRange(50, 100);
    m_opacitySlider->setValue(100);
    QLabel* opacityLabel = new QLabel("100%", this);
    
    connect(m_opacitySlider, &QSlider::valueChanged, [opacityLabel](int value) {
        opacityLabel->setText(QString("%1%").arg(value));
    });
    
    opacityLayout->addWidget(m_opacitySlider);
    opacityLayout->addWidget(opacityLabel);
    appearanceLayout->addRow("窗口透明度:", opacityLayout);
    
    // 系统托盘设置
    m_minimizeToTrayCheck = new QCheckBox("最小化到系统托盘", this);
    m_minimizeToTrayCheck->setChecked(true);
    appearanceLayout->addRow(m_minimizeToTrayCheck);
    
    m_startMinimizedCheck = new QCheckBox("启动时最小化", this);
    appearanceLayout->addRow(m_startMinimizedCheck);
    
    layout()->addWidget(appearanceGroup);
}

void SettingsWidget::setupDataSettings()
{
    QGroupBox* dataGroup = new QGroupBox("数据管理", this);
    QVBoxLayout* dataLayout = new QVBoxLayout(dataGroup);
    
    QLabel* dataLabel = new QLabel("管理您的数据备份和导入导出:", this);
    dataLayout->addWidget(dataLabel);
    
    QHBoxLayout* dataButtonLayout = new QHBoxLayout();
    
    m_exportDataBtn = new QPushButton("导出数据", this);
    m_importDataBtn = new QPushButton("导入数据", this);
    m_backupDataBtn = new QPushButton("备份数据", this);
    m_resetDataBtn = new QPushButton("重置数据", this);
    
    m_resetDataBtn->setStyleSheet("QPushButton { background-color: #f44336; color: white; }");
    
    dataButtonLayout->addWidget(m_exportDataBtn);
    dataButtonLayout->addWidget(m_importDataBtn);
    dataButtonLayout->addWidget(m_backupDataBtn);
    dataButtonLayout->addWidget(m_resetDataBtn);
    dataButtonLayout->addStretch();
    
    dataLayout->addLayout(dataButtonLayout);
    
    layout()->addWidget(dataGroup);
}

void SettingsWidget::connectSignals()
{
    // 危险时段管理
    connect(m_addDangerTimeBtn, &QPushButton::clicked, this, &SettingsWidget::onAddDangerTime);
    connect(m_removeDangerTimeBtn, &QPushButton::clicked, this, &SettingsWidget::onRemoveDangerTime);
    
    // 主题变化
    connect(m_themeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &SettingsWidget::onThemeChanged);
    
    // 数据管理
    connect(m_exportDataBtn, &QPushButton::clicked, this, &SettingsWidget::onExportSettings);
    connect(m_importDataBtn, &QPushButton::clicked, this, &SettingsWidget::onImportSettings);
    connect(m_resetDataBtn, &QPushButton::clicked, this, &SettingsWidget::onResetToDefaults);
    
    // 按钮
    connect(m_saveBtn, &QPushButton::clicked, this, &SettingsWidget::saveSettings);
    connect(m_cancelBtn, &QPushButton::clicked, this, &SettingsWidget::loadSettings);
    connect(m_resetBtn, &QPushButton::clicked, this, &SettingsWidget::onResetToDefaults);
}

void SettingsWidget::loadSettings()
{
    if (!m_dataManager) return;
    
    UserSettings settings = m_dataManager->loadUserSettings();
    
    m_userNameEdit->setText(settings.userName);
    m_goalEdit->setPlainText(settings.goal);
    m_startDateEdit->setDate(settings.startDate);
    
    m_dailyReminderCheck->setChecked(!settings.dailyReminderTime.isNull());
    m_dailyReminderTime->setTime(settings.dailyReminderTime.isValid() ? 
                                 settings.dailyReminderTime : QTime(21, 0));
    
    m_motivationalCheck->setChecked(settings.motivationalEnabled);
    m_motivationalInterval->setValue(settings.motivationalInterval);
    
    // 加载危险时段
    m_dangerTimesList->clear();
    for (const QTime& time : settings.dangerTimes) {
        m_dangerTimesList->addItem(time.toString("hh:mm"));
    }
}

void SettingsWidget::saveSettings()
{
    if (!m_dataManager) return;
    
    UserSettings settings;
    settings.userName = m_userNameEdit->text();
    settings.goal = m_goalEdit->toPlainText();
    settings.startDate = m_startDateEdit->date();
    
    settings.dailyReminderTime = m_dailyReminderCheck->isChecked() ? 
                                m_dailyReminderTime->time() : QTime();
    
    settings.motivationalEnabled = m_motivationalCheck->isChecked();
    settings.motivationalInterval = m_motivationalInterval->value();
    
    // 保存危险时段
    settings.dangerTimes.clear();
    for (int i = 0; i < m_dangerTimesList->count(); ++i) {
        QTime time = QTime::fromString(m_dangerTimesList->item(i)->text(), "hh:mm");
        if (time.isValid()) {
            settings.dangerTimes.append(time);
        }
    }
    
    if (m_dataManager->saveUserSettings(settings)) {
        QMessageBox::information(this, "保存成功", "设置已保存！");
    } else {
        QMessageBox::warning(this, "保存失败", "设置保存失败，请重试。");
    }
}

void SettingsWidget::onAddDangerTime()
{
    QTime time = m_newDangerTime->time();
    QString timeStr = time.toString("hh:mm");
    
    // 检查是否已存在
    for (int i = 0; i < m_dangerTimesList->count(); ++i) {
        if (m_dangerTimesList->item(i)->text() == timeStr) {
            QMessageBox::information(this, "提示", "该时间段已存在！");
            return;
        }
    }
    
    m_dangerTimesList->addItem(timeStr);
}

void SettingsWidget::onRemoveDangerTime()
{
    int currentRow = m_dangerTimesList->currentRow();
    if (currentRow >= 0) {
        delete m_dangerTimesList->takeItem(currentRow);
    }
}

void SettingsWidget::onResetToDefaults()
{
    int ret = QMessageBox::question(this, "确认重置", 
        "确定要恢复默认设置吗？这将清除所有自定义配置。",
        QMessageBox::Yes | QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        // 重置为默认值
        m_userNameEdit->setText("用户");
        m_goalEdit->setPlainText("戒除不良习惯，重获新生");
        m_startDateEdit->setDate(QDate::currentDate());
        
        m_dailyReminderCheck->setChecked(true);
        m_dailyReminderTime->setTime(QTime(21, 0));
        
        m_motivationalCheck->setChecked(true);
        m_motivationalInterval->setValue(2);
        
        m_dangerTimesList->clear();
        m_dangerTimesList->addItem("22:00");
        m_dangerTimesList->addItem("23:00");
        
        m_themeCombo->setCurrentIndex(0);
        m_languageCombo->setCurrentIndex(0);
        m_opacitySlider->setValue(100);
        m_minimizeToTrayCheck->setChecked(true);
        m_startMinimizedCheck->setChecked(false);
    }
}

void SettingsWidget::onExportSettings()
{
    QString fileName = QFileDialog::getSaveFileName(this,
        "导出设置", 
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/settings.json",
        "JSON文件 (*.json)");
    
    if (!fileName.isEmpty()) {
        QMessageBox::information(this, "导出完成", "设置已导出到: " + fileName);
    }
}

void SettingsWidget::onImportSettings()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "导入设置", 
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation),
        "JSON文件 (*.json)");
    
    if (!fileName.isEmpty()) {
        QMessageBox::information(this, "导入完成", "设置已从文件导入: " + fileName);
        loadSettings();
    }
}

void SettingsWidget::onThemeChanged()
{
    // 这里可以实现主题切换逻辑
    int themeIndex = m_themeCombo->currentIndex();
    QString themeName = m_themeCombo->currentText();
    
    // 应用主题（这里只是示例）
    QMessageBox::information(this, "主题切换", 
        QString("主题已切换到: %1\n重启应用后生效。").arg(themeName));
}
